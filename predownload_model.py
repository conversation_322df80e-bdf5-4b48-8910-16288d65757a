#!/usr/bin/env python3
"""Download models from Hugging Face with visible progress"""

import argparse
import os
from typing import Optional
from huggingface_hub import snapshot_download


def download_with_progress(model: str, cache_dir: Optional[str] = None) -> None:
    """
    Download a model from Hugging Face with progress tracking.

    Args:
        model: The model name/path on Hugging Face (e.g., "microsoft/Phi-3-mini-4k-instruct")
        cache_dir: Directory to cache the model (defaults to ~/.cache/huggingface/hub)
    """
    print(f"Downloading {model} with progress tracking...")

    if cache_dir is None:
        cache_dir = os.path.expanduser("~/.cache/huggingface/hub")

    # Download with progress bars
    snapshot_download(
        repo_id=model,
        cache_dir=cache_dir,
        resume_download=True,
        local_files_only=False,
    )

    print("Download complete!")


def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(
        description="Download models from Hugging Face with progress tracking"
    )

    parser.add_argument(
        "model",
        help="Model name/path on Hugging Face (e.g., microsoft/Phi-3-mini-4k-instruct)"
    )

    parser.add_argument(
        "--cache-dir",
        help="Directory to cache the model (default: ~/.cache/huggingface/hub)",
        default=None
    )

    args = parser.parse_args()

    download_with_progress(args.model, args.cache_dir)


if __name__ == "__main__":
    main()